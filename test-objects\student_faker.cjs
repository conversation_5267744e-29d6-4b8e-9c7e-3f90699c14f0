"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateStudentInfo = void 0;
var en_ZA_1 = require("@faker-js/faker/locale/en_ZA");
var storage_utils_1 = require("../lib/storage-utils");
// Generate SA ID Number
var generateSAID = function () {
    // Generate date of birth (YYMMDD)
    var year = en_ZA_1.faker.number.int({ min: 1990, max: 2005 });
    var month = String(en_ZA_1.faker.number.int({ min: 1, max: 12 })).padStart(2, '0');
    var day = String(en_ZA_1.faker.number.int({ min: 1, max: 28 })).padStart(2, '0');
    var dob = "".concat(String(year).slice(-2)).concat(month).concat(day);
    // Generate gender number (SSSS)
    // For females: 0000-4999, For males: 5000-9999
    var isMale = Math.random() < 0.5;
    var genderNumber = isMale
        ? en_ZA_1.faker.number.int({ min: 5000, max: 9999 })
        : en_ZA_1.faker.number.int({ min: 0, max: 4999 });
    var genderStr = String(genderNumber).padStart(4, '0');
    // Citizenship (0 for SA citizen)
    var citizenship = '0';
    // Race indicator (8 for testing purposes)
    var race = '8';
    // Construct ID without checksum (12 digits)
    var idWithoutChecksum = "".concat(dob).concat(genderStr).concat(citizenship).concat(race);
    // Calculate Luhn algorithm check digit
    var checksum = calculateLuhnChecksum(idWithoutChecksum);
    return "".concat(idWithoutChecksum).concat(checksum);
};
var calculateLuhnChecksum = function (idNumber) {
    var sum = 0;
    var alternate = false;
    // Loop through digits from right to left
    for (var i = idNumber.length - 1; i >= 0; i--) {
        var digit = parseInt(idNumber[i]);
        if (alternate) {
            digit *= 2;
            if (digit > 9) {
                digit -= 9;
            }
        }
        sum += digit;
        alternate = !alternate;
    }
    return (10 - (sum % 10)) % 10;
};
// Generate SA cellphone number
var generateCellphone = function () {
    var prefixes = ["61", "62", "64", "71", "72", "74", "82", "83", "84"];
    var prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    var suffix = en_ZA_1.faker.number.int({ min: 1000000, max: 9999999 });
    return "+27" + prefix + suffix;
};
// Generate SA postal code
var generatePostalCode = function () {
    var majorCities = ["0001", "0002", "2000", "2001", "6000", "6001", "8000", "8001"];
    return majorCities[Math.floor(Math.random() * majorCities.length)];
};
// List of common South African suburbs
var suburbs = [
    "Sandton",
    "Rosebank",
    "Bryanston",
    "Norkem Park",
    "Kempton Park",
    "Fourways",
    "Randburg",
    "Centurion",
    "Morningside",
    "Midrand",
    "Waterkloof",
    "Brooklyn",
    "Hatfield",
    "Menlyn",
    "Sunninghill"
];
// Generate physical address with suburb
var generatePhysicalAddress = function () {
    var streetNumber = en_ZA_1.faker.number.int({ min: 1, max: 999 });
    var streetName = en_ZA_1.faker.location.street();
    var suburb = suburbs[Math.floor(Math.random() * suburbs.length)];
    var city = en_ZA_1.faker.location.city();
    return "".concat(streetNumber, " ").concat(streetName, ", ").concat(suburb, ", ").concat(city);
};
var generateStudentInfo = function () {
    var firstName = en_ZA_1.faker.person.firstName();
    var lastName = en_ZA_1.faker.person.lastName();
    var studentInfo = {
        firstName: firstName,
        lastName: lastName,
        cellphone: generateCellphone(),
        idNumber: generateSAID(),
        email: "".concat(firstName.toLowerCase()).concat(lastName.toLowerCase(), "@test.com"),
        password: en_ZA_1.faker.internet.password({ length: 12, prefix: 'Test@' }),
        parentEmail: en_ZA_1.faker.internet.email({ provider: "test.com" }),
        parentMobile: generateCellphone(),
        physicalAddress: generatePhysicalAddress(),
        zipCode: generatePostalCode()
    };
    // Save the generated student info
    storage_utils_1.storageUtils.saveStudent(studentInfo);
    return studentInfo;
};
exports.generateStudentInfo = generateStudentInfo;
// Generate and log student information
var studentInfo = (0, exports.generateStudentInfo)();
console.log(JSON.stringify(studentInfo, null, 2));
// Log total number of generated students
var allStudents = storage_utils_1.storageUtils.getAllStudents();
console.log("\nTotal generated students: ".concat(allStudents.length));
