name: Playwright Production Tests with Allure Report

on:
  push:
    branches:
      - master
      - test-development
  schedule:
    - cron: '0 20 * * 3' # Runs at 20:00 (8 PM) UTC on Wednesdays
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run tests against'
        required: true
        default: 'prod'
        type: choice
        options:
          - prod
          - staging
          - accp
      test_project:
        description: 'Test project to run'
        required: true
        default: 'approved-tests'
        type: choice
        options:
          - approved-tests
          - not-approved-tests

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment:
          - ${{ github.event_name == 'workflow_dispatch' && inputs.environment || 'prod' }}
        test_project:
          - ${{ github.event_name == 'workflow_dispatch' && inputs.test_project || 'approved-tests' }}
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          
      - name: Cache Playwright browsers
        uses: actions/cache@v3
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.OS }}-playwright-${{ hashFiles('**/package-lock.json') }}
          
      - name: System information
        run: |
          echo "OS version: $(lsb_release -d)"
          echo "Available disk space: $(df -h)"
          echo "Available memory: $(free -h)"
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright Browsers
        run: npx playwright install --with-deps
        
      - name: Verify Playwright installation
        run: npx playwright --version
        
      - name: Run Playwright tests
        env:
          ENV: ${{ matrix.environment }}
          URL: ${{ secrets.PROD_URL }}
          AUTHURL: ${{ secrets.PROD_AUTHURL }}
          EXCHANGEURL: ${{ secrets.PROD_EXCHANGE_URL }}
          PASSWORD: ${{ secrets.TEST_PASSWORD }}
          BOTPASSWORD: ${{ secrets.PROD_BOT_PASSWORD }}
        run: npx playwright test --project=${{ matrix.test_project }} --reporter=line,allure-playwright
        
      - name: Get Allure history
        uses: actions/checkout@v4
        if: always()
        continue-on-error: true
        with:
          ref: gh-pages
          path: gh-pages
          
      - name: Generate Allure Report
        uses: simple-elf/allure-report-action@master
        if: always()
        with:
          allure_results: allure-results
          allure_report: allure-report
          keep_reports: 20
          
      # NEW: Create ZIP of Allure Report for email
      - name: Create Allure Report ZIP
        if: always()
        run: |
          cd allure-report
          zip -r ../allure-report-${{ matrix.environment }}-${{ matrix.test_project }}.zip .
          cd ..
          
      # NEW: Send Email with Report
      - name: Send Test Results Email
        if: always()
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: "${{ job.status == 'success' && '✅ SAFE TO DEPLOY' || '❌ BLOCK DEPLOYMENT' }} - ${{ matrix.environment }} Tests - Build ${{ github.run_number }}"
          body: |
            🚦 **Test Results Summary**
            
            **Environment**: ${{ matrix.environment }}
            **Project**: ${{ matrix.test_project }}
            **Build**: ${{ github.run_number }}
            **Status**: ${{ job.status }}
            **Branch**: ${{ github.ref_name }}
            **Commit**: ${{ github.sha }}
            
            **Deployment Decision**: ${{ job.status == 'success' && '✅ SAFE TO DEPLOY' || '❌ BLOCK DEPLOYMENT - DO NOT DEPLOY' }}
            
            📊 **How to View Full Report:**
            1. Download the attached ZIP file
            2. Extract the zip file
            3. Open `index.html` in any web browser
            
            🔗 **GitHub Actions Link**: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
            
            ---
            This is an automated message from the GitHub Actions CI/CD pipeline.
          to: ${{ secrets.EMAIL_TO }}
          from: ${{ secrets.EMAIL_FROM }}
          attachments: allure-report-${{ matrix.environment }}-${{ matrix.test_project }}.zip
          
      - name: Upload Allure Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: allure-report-${{ matrix.environment }}-${{ matrix.test_project }}
          path: allure-report/
          retention-days: 90
          
      - name: Upload Playwright Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report-${{ matrix.environment }}-${{ matrix.test_project }}
          path: playwright-report/
          retention-days: 90